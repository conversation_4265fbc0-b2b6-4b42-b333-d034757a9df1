<template>
  <div class="document-list-container">
    <!-- 搜索和筛选栏 -->
    <div class="search-filter-bar">
      <div class="search-section">
        <el-input
          v-model="searchKeywords"
          placeholder="搜索文档名称..."
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          style="width: 300px"
        />
      </div>
      
      <div class="filter-section">
        <el-select
          v-model="filterStatus"
          placeholder="状态筛选"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>
        
        <el-select
          v-model="filterType"
          placeholder="类型筛选"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option
            v-for="type in typeOptions"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
        
        <el-button
          type="primary"
          :icon="Refresh"
          @click="refreshList"
        >
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 批量操作栏 -->
    <div v-if="selectedDocuments.length > 0" class="batch-actions-bar">
      <div class="selection-info">
        已选择 {{ selectedDocuments.length }} 个文档
      </div>
      <div class="batch-buttons">
        <el-button
          type="success"
          :icon="VideoPlay"
          @click="batchStartParsing"
          :loading="batchLoading"
        >
          批量解析
        </el-button>
        <el-button
          type="warning"
          :icon="VideoPause"
          @click="batchStopParsing"
          :loading="batchLoading"
        >
          停止解析
        </el-button>
        <el-button
          type="danger"
          :icon="Delete"
          @click="batchDelete"
          :loading="batchLoading"
        >
          批量删除
        </el-button>
      </div>
    </div>
    
    <!-- 文档列表 -->
    <div class="document-table-container">
      <el-table
        v-loading="loading"
        :data="documentList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="文档名称" min-width="200" sortable="custom" prop="name">
          <template #default="{ row }">
            <div class="document-name-cell">
              <div class="file-icon">
                <el-icon :size="20" :color="getFileIconColor(row.type)">
                  <component :is="getFileIcon(row.name)" />
                </el-icon>
              </div>
              <div class="file-info">
                <div class="file-name" :title="row.name">{{ row.name }}</div>
                <div class="file-meta">
                  {{ formatFileSize(row.size) }} • {{ getFileTypeLabel(row.type) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="120" sortable="custom" prop="status">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="解析进度" width="150">
          <template #default="{ row }">
            <div v-if="row.status === 'parsing'" class="progress-cell">
              <el-progress
                :percentage="Math.round((row.progress || 0) * 100)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ Math.round((row.progress || 0) * 100) }}%</span>
            </div>
            <div v-else-if="row.status === 'parsed'" class="parsed-info">
              <span class="chunk-count">{{ row.chunk_num || 0 }} 分块</span>
              <span class="token-count">{{ formatNumber(row.token_num || 0) }} Token</span>
            </div>
            <span v-else class="status-text">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="解析器" width="100">
          <template #default="{ row }">
            <el-tag size="small" type="info">
              {{ getParserLabel(row.parser_id) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="160" sortable="custom" prop="create_time">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                size="small"
                @click="previewDocument(row)"
              >
                预览
              </el-button>

              <el-button
                v-if="row.status === 'parsed'"
                link
                size="small"
                type="success"
                @click="viewDocumentResult(row)"
              >
                查看结果
              </el-button>

              <el-button
                v-if="row.status === 'uploaded' || row.status === 'failed'"
                link
                size="small"
                @click="startParsing(row)"
                :loading="row.parsing"
              >
                解析
              </el-button>

              <el-button
                v-if="row.status === 'parsing'"
                link
                size="small"
                @click="stopParsing(row)"
                :loading="row.stopping"
              >
                停止
              </el-button>

              <el-dropdown @command="(command: string) => handleAction(command, row)">
                <el-button link size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="download">下载</el-dropdown-item>
                    <el-dropdown-item command="reparse">重新解析</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="currentPreviewDoc?.name || '文档预览'"
      :width="dialogWidth"
      top="5vh"
      :before-close="closePreview"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      draggable
      destroy-on-close
    >


      <div class="enhanced-preview-container" :class="{ 'fullscreen': isFullscreen }">
        <DocumentPreview
          v-if="currentPreviewDoc"
          :document="currentPreviewDoc"
          @document-type-detected="handleDocumentTypeDetected"
        />
      </div>
    </el-dialog>
    
    <!-- 编辑文档对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑文档"
      width="500px"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="文档名称">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="解析器">
          <el-select v-model="editForm.parser_id" style="width: 100%">
            <el-option
              v-for="parser in parserOptions"
              :key="parser.value"
              :label="parser.label"
              :value="parser.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit" :loading="editLoading">保存</el-button>
      </template>
    </el-dialog>

    <!-- 查看结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="`解析结果 - ${currentResultDoc?.name || '文档'}`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentResultDoc" class="result-content">
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="文档名称" :span="2">
            {{ currentResultDoc.name }}
          </el-descriptions-item>
          <el-descriptions-item label="分块数量">
            {{ currentResultDoc.chunk_num || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="Token数量">
            {{ formatNumber(currentResultDoc.token_num || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="文档大小">
            {{ formatFileSize(currentResultDoc.size || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="解析状态">
            <el-tag :type="getStatusTagType(currentResultDoc.status || '')">
              {{ getStatusLabel(currentResultDoc.status || '') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="解析时长" :span="2">
            {{ calculateDuration(currentResultDoc) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="resultDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Search,
  Refresh,
  VideoPlay,
  VideoPause,
  Delete,
  Document,
  ArrowDown,
  Files,
  Notebook,
  Grid,
  Reading,
  Memo,
  DataAnalysis
} from '@element-plus/icons-vue';

import {
  getDocumentList,
  deleteDocuments,
  updateDocument,
  startDocumentParsing,
  stopDocumentParsing,
  reparseDocument as reparseDocumentAPI,
  getParserOptions,
  formatFileSize,
  type DocumentInfo,
  type DocumentQueryParams
} from '/@/api/iot/document';

import DocumentPreview from './DocumentPreview.vue';

// Props
interface Props {
  knowledgeBaseId: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  documentSelect: [documents: DocumentInfo[]];
  documentAction: [action: string, document: DocumentInfo];
  documentListUpdate: [documents: DocumentInfo[]];
}>();

// 响应式数据
const loading = ref(false);
const batchLoading = ref(false);
const documentList = ref<DocumentInfo[]>([]);
const selectedDocuments = ref<DocumentInfo[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 搜索和筛选
const searchKeywords = ref('');
const filterStatus = ref('');
const filterType = ref('');

// 排序
const sortField = ref('create_time');
const sortOrder = ref('desc');

// 预览相关
const previewDialogVisible = ref(false);
const currentPreviewDoc = ref<DocumentInfo | null>(null);
const isFullscreen = ref(false);
const documentOrientation = ref<'portrait' | 'landscape'>('portrait');

// 动态弹窗宽度
const dialogWidth = computed(() => {
  if (documentOrientation.value === 'portrait') {
    // 竖向文档（PDF、Word）使用较小宽度，减少左右空白
    return '75%';
  } else {
    // 横向文档（Excel、PowerPoint）使用较大宽度
    return '90%';
  }
});

// 轮询相关状态
const pollingTimer = ref<NodeJS.Timeout | null>(null);
const pollingInterval = 10000; // 轮询间隔10秒（大幅降低服务器压力）
const isPolling = ref(false);

// 编辑相关
const editDialogVisible = ref(false);
const editLoading = ref(false);
const editForm = reactive({
  name: '',
  parser_id: ''
});
const currentEditDoc = ref<DocumentInfo | null>(null);

// 查看结果相关
const resultDialogVisible = ref(false);
const currentResultDoc = ref<DocumentInfo | null>(null);

// 选项数据
const statusOptions = [
  { label: '已上传', value: 'uploaded' },
  { label: '解析中', value: 'parsing' },
  { label: '已解析', value: 'parsed' },
  { label: '失败', value: 'failed' },
  { label: '已取消', value: 'cancelled' }
];

const typeOptions = [
  { label: 'PDF', value: 'pdf' },
  { label: 'Word', value: 'docx' },
  { label: 'Excel', value: 'xlsx' },
  { label: 'PowerPoint', value: 'pptx' },
  { label: '文本', value: 'txt' },
  { label: 'Markdown', value: 'md' }
];

const parserOptions = getParserOptions();

// 计算属性
const queryParams = computed((): DocumentQueryParams => ({
  kb_id: props.knowledgeBaseId,
  page: currentPage.value,
  page_size: pageSize.value,
  orderby: sortField.value,
  desc: sortOrder.value === 'desc',
  keywords: searchKeywords.value || undefined,
  status: filterStatus.value || undefined,
  type: filterType.value || undefined
}));

// 方法
const loadDocumentList = async () => {
  if (!props.knowledgeBaseId) return;
  
  loading.value = true;
  try {
    const response = await getDocumentList(queryParams.value);
    const businessData = response.data;

    if (businessData.code === 200) {
      // 转换RAGFlow数据格式到前端期望格式
      const rawDocs = businessData.data?.docs || [];
      documentList.value = rawDocs.map((doc: any) => ({
        ...doc,
        chunk_num: doc.chunk_count,
        token_num: doc.token_count,
        status: mapRAGFlowStatus(doc.run, doc.status),
        kb_id: props.knowledgeBaseId, // 添加知识库ID
        // 确保解析时长相关字段被正确映射
        process_begin_at: doc.process_begin_at,
        process_duation: doc.process_duation
      }));
      total.value = businessData.data?.total || 0;

      // 触发文档列表更新事件
      emit('documentListUpdate', documentList.value);

      // 检查是否需要开始轮询
      checkAndStartPolling();
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取文档列表失败');
    }
  } catch (error) {
    ElMessage.error('获取文档列表失败');
    console.error('Load document list error:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadDocumentList();
};

const handleFilter = () => {
  currentPage.value = 1;
  loadDocumentList();
};

const refreshList = () => {
  loadDocumentList();
};

// 轮询相关函数 - 传统稳定方案
const startPolling = () => {
  if (isPolling.value || pollingTimer.value) return;

  isPolling.value = true;
  pollingTimer.value = setInterval(() => {
    // 只有当有文档正在解析时才轮询
    const hasParsingDocs = documentList.value.some(doc => doc.status === 'parsing');
    if (hasParsingDocs) {
      loadDocumentList();
    } else {
      // 没有解析中的文档时停止轮询
      stopPolling();
    }
  }, pollingInterval);
};

const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
  isPolling.value = false;
};

const checkAndStartPolling = () => {
  // 检查是否有解析中的文档，如果有则开始轮询
  const hasParsingDocs = documentList.value.some(doc => doc.status === 'parsing');
  if (hasParsingDocs && !isPolling.value) {
    startPolling();
  }
};

const handleSelectionChange = (selection: DocumentInfo[]) => {
  selectedDocuments.value = selection;
  emit('documentSelect', selection);
};

const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop || 'create_time';
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc';
  loadDocumentList();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadDocumentList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadDocumentList();
};

// 文档操作
const startParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  doc.parsing = true;
  try {
    const response = await startDocumentParsing({
      kb_id: props.knowledgeBaseId,
      doc_id: doc.id,
      parser_id: doc.parser_id
    });
    
    if (response.data.code === 200) {
      ElMessage.success('开始解析文档');
      await loadDocumentList();
      // 开始解析后立即启动轮询
      startPolling();
    } else {
      ElMessage.error(response.data.msg || '开始解析失败');
    }
  } catch (error) {
    ElMessage.error('开始解析失败');
  } finally {
    doc.parsing = false;
  }
};

const stopParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;

  doc.stopping = true;
  try {
    const response = await stopDocumentParsing(props.knowledgeBaseId, doc.id);

    if (response.data.code === 200) {
      ElMessage.success('停止解析文档');
      await loadDocumentList();
    } else {
      ElMessage.error(response.data.msg || '停止解析失败');
    }
  } catch (error) {
    ElMessage.error('停止解析失败');
  } finally {
    doc.stopping = false;
  }
};

/**
 * 重新解析文档 - 使用优化的API
 */
const reparseDocument = async (doc: DocumentInfo) => {
  if (!doc.id) return;

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要重新解析文档"${doc.name}"吗？这将删除现有的分块数据并重新开始解析。`,
      '重新解析确认',
      {
        confirmButtonText: '确定重新解析',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--warning'
      }
    );

    doc.parsing = true;

    try {
      console.log(`🔄 开始重新解析文档: ${doc.name}`);

      // 使用优化的重新解析API
      const response = await reparseDocumentAPI(
        props.knowledgeBaseId,
        doc.id,
        doc.parser_id || 'naive'
      );

      if (response.data.code === 200) {
        ElMessage.success(`开始重新解析文档: ${doc.name}`);

        // 刷新文档列表
        await loadDocumentList();

        // 启动轮询监控解析进度
        startPolling();

        console.log(`✅ 重新解析启动成功: ${doc.name}`);
      } else {
        throw new Error(response.data.msg || '重新解析启动失败');
      }

    } catch (error: any) {
      console.error('重新解析失败:', error);
      ElMessage.error(error.message || '重新解析失败，请稍后重试');
    }

  } catch (error) {
    // 用户取消操作
    if (error !== 'cancel') {
      console.error('重新解析操作失败:', error);
    }
  } finally {
    doc.parsing = false;
  }
};

const previewDocument = (doc: DocumentInfo) => {
  currentPreviewDoc.value = doc;
  previewDialogVisible.value = true;
};

const viewDocumentResult = (doc: DocumentInfo) => {
  currentResultDoc.value = doc;
  resultDialogVisible.value = true;
};

// 计算解析时长
const calculateDuration = (doc: DocumentInfo): string => {
  // 优先使用 process_duation 字段（RAGFlow提供的真实解析时长）
  if (doc.process_duation && doc.process_duation > 0) {
    const totalSeconds = Math.floor(doc.process_duation);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes}分${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  // 如果没有 process_duation，但有 process_begin_at，尝试计算
  if (doc.process_begin_at && doc.update_time) {
    try {
      const beginTime = new Date(doc.process_begin_at).getTime();
      const endTime = new Date(doc.update_time).getTime();
      const duration = endTime - beginTime;

      if (duration > 0) {
        const totalSeconds = Math.floor(duration / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;

        if (minutes > 0) {
          return `${minutes}分${seconds}秒`;
        } else {
          return `${seconds}秒`;
        }
      }
    } catch (error) {
      console.warn('解析时长计算失败:', error);
    }
  }

  // 如果都没有，返回未知
  return '未知';
};

const closePreview = () => {
  previewDialogVisible.value = false;
  currentPreviewDoc.value = null;
  isFullscreen.value = false;
  // 重置文档方向
  documentOrientation.value = 'portrait';
};

const handleDocumentTypeDetected = (type: 'portrait' | 'landscape') => {
  console.log('文档列表检测到文档方向:', type);
  documentOrientation.value = type;
};



const handleAction = async (command: string, doc: DocumentInfo) => {
  emit('documentAction', command, doc);

  switch (command) {
    case 'edit':
      editDocument(doc);
      break;
    case 'download':
      downloadDocument(doc);
      break;
    case 'reparse':
      await reparseDocument(doc);
      break;
    case 'delete':
      await deleteDocument(doc);
      break;
  }
};

const editDocument = (doc: DocumentInfo) => {
  currentEditDoc.value = doc;
  editForm.name = doc.name;
  editForm.parser_id = doc.parser_id || 'naive';
  editDialogVisible.value = true;
};

const saveEdit = async () => {
  if (!currentEditDoc.value?.id) return;
  
  editLoading.value = true;
  try {
    const response = await updateDocument(
      props.knowledgeBaseId,
      currentEditDoc.value.id,
      {
        name: editForm.name,
        parser_id: editForm.parser_id
      }
    );
    
    if (response.data.code === 200) {
      ElMessage.success('更新文档成功');
      editDialogVisible.value = false;
      await loadDocumentList();
    } else {
      ElMessage.error(response.data.msg || '更新文档失败');
    }
  } catch (error) {
    ElMessage.error('更新文档失败');
  } finally {
    editLoading.value = false;
  }
};

const downloadDocument = (doc: DocumentInfo) => {
  // 实现文档下载
  const downloadUrl = `/api/documents/${doc.id}/download`;
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = doc.name;
  link.click();
};

const deleteDocument = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要删除文档 "${doc.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const response = await deleteDocuments({
      kb_id: props.knowledgeBaseId,
      doc_ids: [doc.id]
    });

    if (response.data.code === 200) {
      ElMessage.success('删除文档成功');
      await loadDocumentList();
    } else {
      ElMessage.error(response.data.msg || '删除文档失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除文档失败');
    }
  }
};

// 批量操作
const batchStartParsing = async () => {
  if (selectedDocuments.value.length === 0) return;

  // 筛选出可以解析的文档
  const parseableDocs = selectedDocuments.value.filter(doc =>
    doc.id && (doc.status === 'uploaded' || doc.status === 'failed')
  );

  if (parseableDocs.length === 0) {
    ElMessage.warning('选中的文档中没有可以解析的文档（只有"已上传"或"解析失败"状态的文档可以解析）');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要开始解析选中的 ${parseableDocs.length} 个文档吗？`,
      '批量解析确认',
      {
        confirmButtonText: '开始解析',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    batchLoading.value = true;

    // 逐个启动解析，避免并发过多
    let successCount = 0;
    let failCount = 0;

    for (const doc of parseableDocs) {
      try {
        await startDocumentParsing({
          kb_id: props.knowledgeBaseId,
          doc_id: doc.id!,
          parser_id: doc.parser_id
        });
        successCount++;
      } catch (error) {
        failCount++;
        console.error(`解析文档 ${doc.name} 失败:`, error);
      }
    }

    // 显示结果
    if (successCount > 0) {
      ElMessage.success(`成功开始解析 ${successCount} 个文档${failCount > 0 ? `，${failCount} 个失败` : ''}`);
      await loadDocumentList();
      // 批量开始解析后启动轮询
      startPolling();
    } else {
      ElMessage.error('所有文档解析启动失败');
    }

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量解析操作失败');
    }
  } finally {
    batchLoading.value = false;
  }
};

const batchStopParsing = async () => {
  if (selectedDocuments.value.length === 0) return;

  // 筛选出正在解析的文档
  const parsingDocs = selectedDocuments.value.filter(doc =>
    doc.id && doc.status === 'parsing'
  );

  if (parsingDocs.length === 0) {
    ElMessage.warning('选中的文档中没有正在解析的文档');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要停止解析选中的 ${parsingDocs.length} 个文档吗？`,
      '批量停止解析确认',
      {
        confirmButtonText: '停止解析',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    batchLoading.value = true;

    // 逐个停止解析
    let successCount = 0;
    let failCount = 0;

    for (const doc of parsingDocs) {
      try {
        await stopDocumentParsing(props.knowledgeBaseId, doc.id!);
        successCount++;
      } catch (error) {
        failCount++;
        console.error(`停止解析文档 ${doc.name} 失败:`, error);
      }
    }

    // 显示结果
    if (successCount > 0) {
      ElMessage.success(`成功停止解析 ${successCount} 个文档${failCount > 0 ? `，${failCount} 个失败` : ''}`);
      await loadDocumentList();
    } else {
      ElMessage.error('所有文档停止解析失败');
    }

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量停止解析操作失败');
    }
  } finally {
    batchLoading.value = false;
  }
};

const batchDelete = async () => {
  if (selectedDocuments.value.length === 0) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocuments.value.length} 个文档吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    batchLoading.value = true;
    const docIds = selectedDocuments.value.map(doc => doc.id!).filter(Boolean);
    
    const response = await deleteDocuments({
      kb_id: props.knowledgeBaseId,
      doc_ids: docIds
    });
    
    if (response.data.code === 200) {
      ElMessage.success(`删除 ${docIds.length} 个文档成功`);
      await loadDocumentList();
    } else {
      ElMessage.error(response.data.msg || '批量删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败');
    }
  } finally {
    batchLoading.value = false;
  }
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    uploaded: 'info',
    parsing: 'warning',
    parsed: 'success',
    failed: 'danger',
    cancelled: 'info'
  };
  return typeMap[status] || 'info';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    uploaded: '已上传',
    parsing: '解析中',
    parsed: '已解析',
    failed: '失败',
    cancelled: '已取消'
  };
  return labelMap[status] || status;
};

const getParserLabel = (parserId: string) => {
  const parser = parserOptions.find(p => p.value === parserId);
  return parser?.label || parserId;
};

const getFileTypeLabel = (mimeType: string) => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/msword': 'Word',
    'text/plain': 'TXT',
    'text/markdown': 'Markdown'
  };
  return typeMap[mimeType] || '文档';
};

// 根据文件名获取对应的图标组件
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();

  const iconMap: Record<string, any> = {
    // PDF文档
    'pdf': Reading,
    // Word文档
    'doc': Notebook,
    'docx': Notebook,
    // Excel表格
    'xls': DataAnalysis,
    'xlsx': DataAnalysis,
    // PowerPoint演示文稿
    'ppt': Grid,
    'pptx': Grid,
    // 文本文件
    'txt': Memo,
    'md': Memo,
    'markdown': Memo,
    // 其他文档
    'html': Files,
    'htm': Files,
    'csv': DataAnalysis,
    'json': Files,
    'xml': Files
  };

  return iconMap[ext || ''] || Document;
};

const getFileIconColor = (mimeType: string) => {
  if (mimeType?.includes('pdf')) return '#F56C6C';
  if (mimeType?.includes('word')) return '#409EFF';
  if (mimeType?.includes('sheet') || mimeType?.includes('excel')) return '#67C23A';
  if (mimeType?.includes('text')) return '#909399';
  if (mimeType?.includes('powerpoint') || mimeType?.includes('presentation')) return '#FF8C00';
  return '#409EFF';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// RAGFlow状态映射到前端状态
const mapRAGFlowStatus = (run: string, status: string) => {
  // run字段：RUNNING, DONE, FAIL, CANCEL
  // status字段：'0'(未开始), '1'(已完成), '2'(失败)
  if (run === 'RUNNING') return 'parsing';
  if (run === 'DONE' && status === '1') return 'parsed';
  if (run === 'FAIL' || status === '2') return 'failed';
  if (run === 'CANCEL') return 'cancelled';
  return 'uploaded'; // 默认状态
};

// 生命周期
onMounted(() => {
  loadDocumentList();
});

// 监听知识库ID变化
watch(() => props.knowledgeBaseId, () => {
  if (props.knowledgeBaseId) {
    currentPage.value = 1;
    loadDocumentList();
  }
});

// 组件卸载时清理轮询
onUnmounted(() => {
  stopPolling();
});

// 暴露方法给父组件
defineExpose({
  refreshList,
  batchStartParsing,
  clearSelection: () => {
    selectedDocuments.value = [];
  }
});
</script>

<style scoped>
.document-list-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.filter-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.selection-info {
  color: #409EFF;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.document-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.parsed-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.chunk-count, .token-count {
  font-size: 12px;
  color: #606266;
}

.status-text {
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.preview-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-dialog-actions {
  display: flex;
  gap: 8px;
}

.enhanced-preview-container {
  height: 70vh;
  min-height: 500px;
}

.enhanced-preview-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  height: 100vh;
}
</style>
